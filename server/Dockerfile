FROM oven/bun:1 AS development

WORKDIR /apps

COPY package.json bun.lock* ./
COPY server/tsconfig.json ./server/
COPY server/package.json ./server/
COPY app/package.json ./app/

COPY server ./server

RUN bun install --frozen-lockfile

WORKDIR /apps/server

EXPOSE 4000

CMD ["bun", "run", "dev"]

FROM oven/bun:1 AS build

WORKDIR /apps

# Cache packages installation
COPY package.json bun.lock* ./
COPY server/tsconfig.json ./server/
COPY server/package.json ./server/
COPY app/package.json ./app/

RUN bun install --frozen-lockfile

COPY server ./server

ENV NODE_ENV=production

RUN bun build \
	--compile \
	--minify-whitespace \
	--minify-syntax \
	--target bun \
	--outfile server-prod \
	./server/src/index.ts

FROM gcr.io/distroless/base

WORKDIR /app

COPY --from=build /apps/server server-prod

ENV NODE_ENV=production

CMD ["./server-prod"]

EXPOSE 4000