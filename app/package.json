{"name": "home-server-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 5173", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@iconify/vue": "^5.0.0", "axios": "^1.11.0", "better-auth": "^1.3.5", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/jsdom": "^21.1.7", "@types/node": "^24.2.1", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "@vitejs/plugin-vue": "^6.0.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "8", "eslint-config-prettier": "^10.1.8", "eslint-plugin-vue": "^10.4.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "vite": "^7.1.2", "vitest": "^3.2.4", "vue-tsc": "^3.0.5"}}