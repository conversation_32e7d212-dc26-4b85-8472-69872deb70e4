import { ref, computed } from 'vue'

export function useLoading(initialState = false) {
  const isLoading = ref(initialState)
  const loadingText = ref('Loading...')
  const progress = ref(0)

  const setLoading = (state: boolean, text?: string) => {
    isLoading.value = state
    if (text) {
      loadingText.value = text
    }
  }

  const setProgress = (value: number) => {
    progress.value = Math.max(0, Math.min(100, value))
  }

  const incrementProgress = (amount = 10) => {
    setProgress(progress.value + amount)
  }

  const resetProgress = () => {
    progress.value = 0
  }

  const isComplete = computed(() => progress.value >= 100)

  return {
    isLoading,
    loadingText,
    progress,
    isComplete,
    setLoading,
    setProgress,
    incrementProgress,
    resetProgress,
  }
}
