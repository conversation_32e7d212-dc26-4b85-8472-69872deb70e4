import { ref, onMounted } from 'vue'

export type Theme = 'light' | 'dark'

const THEME_KEY = 'hs-theme'

export function useTheme() {
  const isDark = ref(false)
  const theme = ref<Theme>('light')

  const applyTheme = (mode: Theme) => {
    theme.value = mode
    isDark.value = mode === 'dark'
    const root = document.documentElement
    
    // Remove existing theme classes
    root.classList.remove('light', 'dark')
    
    // Add new theme class
    root.classList.add(mode)
    
    // Update color scheme meta tag for better browser integration
    const colorSchemeMetaTag = document.querySelector('meta[name="color-scheme"]')
    if (colorSchemeMetaTag) {
      colorSchemeMetaTag.setAttribute('content', mode)
    } else {
      const meta = document.createElement('meta')
      meta.name = 'color-scheme'
      meta.content = mode
      document.head.appendChild(meta)
    }
    
    try {
      localStorage.setItem(THEME_KEY, mode)
    } catch {}
  }

  const toggleTheme = () => {
    applyTheme(isDark.value ? 'light' : 'dark')
  }

  const initTheme = () => {
    try {
      const saved = localStorage.getItem(THEME_KEY) as Theme | null
      if (saved === 'light' || saved === 'dark') {
        applyTheme(saved)
        return
      }
    } catch {}

    // Fallback to system preference
    const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
    applyTheme(prefersDark ? 'dark' : 'light')
  }

  onMounted(() => initTheme())

  return { isDark, theme, applyTheme, toggleTheme, initTheme }
}
