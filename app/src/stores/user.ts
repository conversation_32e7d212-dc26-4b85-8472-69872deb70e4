import { defineStore } from "pinia";
import { computed } from "vue";
import { auth } from "@/auth";

export const useUserStore = defineStore("user", () => {
  // Leverage better-auth reactive session in a central store
  const session = auth.useSession(); // { data, isPending, error, refetch }

  const user = computed(() => session.data?.user ?? null);
  const isLoggedIn = computed(() => Boolean(session.data?.user));

  return {
    user,
    isLoggedIn,
    session, // expose for loading/error if needed
  };
});

