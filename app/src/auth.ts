import { createAuthClient } from "better-auth/client";

const baseURL = import.meta.env.VITE_API_BASE_URL || "http://localhost:3333/api";

export const auth = createAuthClient({
  baseURL,
  // Configure the endpoints that better-auth will use
  endpoints: {
    signIn: "/auth/sign-in",
    signUp: "/auth/sign-up", 
    signOut: "/auth/sign-out",
    getSession: "/auth/session",
    refreshToken: "/auth/refresh",
  },
});

export const { signIn, signOut, signUp, useSession } = auth;

// Initialize auth - ensures session is restored on app start
let authInitialized = false;
export const initAuth = async () => {
  if (authInitialized) return;
  
  try {
    // This will restore the session if valid tokens exist
    await auth.getSession();
    authInitialized = true;
  } catch (error) {
    console.warn('Failed to initialize auth session:', error);
    authInitialized = true; // Still mark as initialized to avoid repeated attempts
  }
};