<script setup lang="ts">
import { useRouter } from "vue-router";
import { auth } from "@/auth";

const router = useRouter();
const session = (auth as any).useSession(); // reactive: { data, isPending, error }

async function logout() {
  await auth.signOut({
    fetchOptions: {
      onSuccess: () => { router.push("/login"); },
    },
  });
}
</script>

<template>
  <div class="min-h-screen p-4 sm:p-6 lg:p-8">
    <div class="container max-w-7xl">
      <!-- Header -->
      <div class="mb-6 sm:mb-8 animate-slide-down">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-2 transition-colors duration-300"
                :style="{ color: 'var(--text)' }">
              Dashboard
            </h1>
            <p class="text-base sm:text-lg transition-colors duration-300"
               :style="{ color: 'var(--text-secondary)' }">
              Welcome to your Home Server Admin Panel
            </p>
          </div>
          <button
            @click="logout"
            class="btn-secondary group relative overflow-hidden rounded-xl font-medium py-2.5 px-4 sm:py-3 sm:px-6 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-opacity-50 self-start sm:self-auto"
            :style="{ '--tw-ring-color': 'var(--accent)' }"
          >
            <div class="absolute inset-0 bg-gradient-to-r from-red-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div class="relative flex items-center gap-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
              </svg>
              <span class="text-sm sm:text-base">Logout</span>
            </div>
          </button>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8">
        <!-- Profile Card -->
        <div class="xl:col-span-1 animate-scale-in" style="animation-delay: 0.1s">
          <div class="glass-card p-6 transition-all duration-500 hover:shadow-2xl">
            <div class="text-center">
              <div class="mx-auto w-16 h-16 sm:w-20 sm:h-20 rounded-2xl flex items-center justify-center shadow-lg mb-4 animate-float transition-all duration-300"
                   :style="{ backgroundColor: 'var(--accent)', color: 'white' }">
                <svg class="w-8 h-8 sm:w-10 sm:h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <h2 class="text-lg sm:text-xl font-semibold mb-4 transition-colors duration-300"
                  :style="{ color: 'var(--text)' }">
                User Profile
              </h2>

              <div v-if="session.isPending" class="space-y-3">
                <div class="animate-pulse">
                  <div class="h-4 rounded w-3/4 mx-auto mb-2 transition-colors duration-300"
                       :style="{ backgroundColor: 'var(--bg-tertiary)' }"></div>
                  <div class="h-4 rounded w-1/2 mx-auto transition-colors duration-300"
                       :style="{ backgroundColor: 'var(--bg-tertiary)' }"></div>
                </div>
              </div>

              <div v-else-if="!session.data" class="p-4 rounded-xl backdrop-blur-sm border transition-all duration-300"
                   :style="{ 
                     backgroundColor: 'rgba(239, 68, 68, 0.1)', 
                     borderColor: 'rgba(239, 68, 68, 0.3)' 
                   }">
                <p class="text-sm text-red-400">No active session</p>
              </div>

              <div v-else class="space-y-3 text-left">
                <div class="p-3 sm:p-4 rounded-xl transition-all duration-300"
                     :style="{ 
                       backgroundColor: 'var(--bg-secondary)', 
                       borderColor: 'var(--border)' 
                     }"
                     class="border">
                  <span class="text-xs sm:text-sm transition-colors duration-300"
                        :style="{ color: 'var(--text-muted)' }">User ID</span>
                  <p class="font-mono text-xs sm:text-sm break-all transition-colors duration-300"
                     :style="{ color: 'var(--text)' }">{{ session.data.user.id }}</p>
                </div>
                <div class="p-3 sm:p-4 rounded-xl transition-all duration-300"
                     :style="{ 
                       backgroundColor: 'var(--bg-secondary)', 
                       borderColor: 'var(--border)' 
                     }"
                     class="border">
                  <span class="text-xs sm:text-sm transition-colors duration-300"
                        :style="{ color: 'var(--text-muted)' }">Email</span>
                  <p class="text-sm sm:text-base break-all transition-colors duration-300"
                     :style="{ color: 'var(--text)' }">{{ session.data.user.email }}</p>
                </div>
                <div v-if="session.data.user.name" class="p-3 sm:p-4 rounded-xl transition-all duration-300"
                     :style="{ 
                       backgroundColor: 'var(--bg-secondary)', 
                       borderColor: 'var(--border)' 
                     }"
                     class="border">
                  <span class="text-xs sm:text-sm transition-colors duration-300"
                        :style="{ color: 'var(--text-muted)' }">Name</span>
                  <p class="text-sm sm:text-base transition-colors duration-300"
                     :style="{ color: 'var(--text)' }">{{ session.data.user.name }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Server Stats -->
        <div class="xl:col-span-2 space-y-6">
          <!-- Quick Stats -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 animate-slide-up" style="animation-delay: 0.2s">
            <div class="glass-card p-4 sm:p-6 transition-all duration-500 hover:shadow-xl">
              <div class="flex items-center">
                <div class="p-2 sm:p-3 rounded-xl transition-all duration-300"
                     :style="{ 
                       backgroundColor: 'rgba(34, 197, 94, 0.1)', 
                       borderColor: 'rgba(34, 197, 94, 0.3)' 
                     }"
                     class="border">
                  <svg class="w-5 h-5 sm:w-6 sm:h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div class="ml-3 sm:ml-4">
                  <p class="text-xs sm:text-sm transition-colors duration-300"
                     :style="{ color: 'var(--text-muted)' }">Server Status</p>
                  <p class="font-semibold text-sm sm:text-base transition-colors duration-300"
                     :style="{ color: 'var(--text)' }">Online</p>
                </div>
              </div>
            </div>

            <div class="glass-card p-4 sm:p-6 transition-all duration-500 hover:shadow-xl">
              <div class="flex items-center">
                <div class="p-2 sm:p-3 rounded-xl transition-all duration-300"
                     :style="{ 
                       backgroundColor: 'rgba(59, 130, 246, 0.1)', 
                       borderColor: 'rgba(59, 130, 246, 0.3)' 
                     }"
                     class="border">
                  <svg class="w-5 h-5 sm:w-6 sm:h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <div class="ml-3 sm:ml-4">
                  <p class="text-xs sm:text-sm transition-colors duration-300"
                     :style="{ color: 'var(--text-muted)' }">Uptime</p>
                  <p class="font-semibold text-sm sm:text-base transition-colors duration-300"
                     :style="{ color: 'var(--text)' }">99.9%</p>
                </div>
              </div>
            </div>

            <div class="glass-card p-4 sm:p-6 transition-all duration-500 hover:shadow-xl sm:col-span-2 lg:col-span-1">
              <div class="flex items-center">
                <div class="p-2 sm:p-3 rounded-xl transition-all duration-300"
                     :style="{ 
                       backgroundColor: 'rgba(139, 92, 246, 0.1)', 
                       borderColor: 'rgba(139, 92, 246, 0.3)' 
                     }"
                     class="border">
                  <svg class="w-5 h-5 sm:w-6 sm:h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                </div>
                <div class="ml-3 sm:ml-4">
                  <p class="text-xs sm:text-sm transition-colors duration-300"
                     :style="{ color: 'var(--text-muted)' }">Services</p>
                  <p class="font-semibold text-sm sm:text-base transition-colors duration-300"
                     :style="{ color: 'var(--text)' }">12 Active</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="glass-card p-6 transition-all duration-500 hover:shadow-xl animate-slide-up" style="animation-delay: 0.3s">
            <h3 class="text-lg sm:text-xl font-semibold mb-4 sm:mb-6 transition-colors duration-300"
                :style="{ color: 'var(--text)' }">Recent Activity</h3>
            <div class="space-y-3 sm:space-y-4">
              <div class="flex items-center p-3 sm:p-4 rounded-xl transition-all duration-300"
                   :style="{ 
                     backgroundColor: 'var(--bg-secondary)', 
                     borderColor: 'var(--border)' 
                   }"
                   class="border">
                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-3 flex-shrink-0"></div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm sm:text-base transition-colors duration-300"
                     :style="{ color: 'var(--text)' }">User logged in successfully</p>
                  <p class="text-xs sm:text-sm transition-colors duration-300"
                     :style="{ color: 'var(--text-muted)' }">Just now</p>
                </div>
              </div>
              <div class="flex items-center p-3 sm:p-4 rounded-xl transition-all duration-300"
                   :style="{ 
                     backgroundColor: 'var(--bg-secondary)', 
                     borderColor: 'var(--border)' 
                   }"
                   class="border">
                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3 flex-shrink-0"></div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm sm:text-base transition-colors duration-300"
                     :style="{ color: 'var(--text)' }">System backup completed</p>
                  <p class="text-xs sm:text-sm transition-colors duration-300"
                     :style="{ color: 'var(--text-muted)' }">2 hours ago</p>
                </div>
              </div>
              <div class="flex items-center p-3 sm:p-4 rounded-xl transition-all duration-300"
                   :style="{ 
                     backgroundColor: 'var(--bg-secondary)', 
                     borderColor: 'var(--border)' 
                   }"
                   class="border">
                <div class="w-2 h-2 bg-purple-500 rounded-full mr-3 flex-shrink-0"></div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm sm:text-base transition-colors duration-300"
                     :style="{ color: 'var(--text)' }">Security scan completed</p>
                  <p class="text-xs sm:text-sm transition-colors duration-300"
                     :style="{ color: 'var(--text-muted)' }">6 hours ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>