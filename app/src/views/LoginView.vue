<script setup lang="ts">
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { auth } from "@/auth";

const router = useRouter();
const route = useRoute();

const email = ref("");
const password = ref("");
const loading = ref(false);
const errorMsg = ref("");

async function onSubmit() {
  loading.value = true;
  errorMsg.value = "";
  try {
    const { error } = await auth.signIn.email(
      {
        email: email.value.trim(),
        password: password.value,
      },
      {
        onError: (ctx) => {
          errorMsg.value = ctx.error?.message || "Login failed";
        },
      },
    );

    if (!error) {
      const redirect = (route.query.redirect as string) || "/profile";
      router.push(redirect);
    }
  } catch (e) {
    errorMsg.value = "Unable to sign in. Please try again.";
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center p-4 sm:p-6 lg:p-8 relative">
    <!-- Login Card -->
    <div class="w-full max-w-sm sm:max-w-md animate-scale-in">
      <div class="glass-card p-6 sm:p-8 relative overflow-hidden group transition-all duration-500 hover:shadow-2xl">
        
        <!-- Animated border glow -->
        <div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-gradient-shift bg-[length:200%_200%]" 
             :style="{ backgroundImage: 'linear-gradient(90deg, rgba(99,102,241,0.3), rgba(139,92,246,0.3), rgba(6,182,212,0.3))' }">
        </div>

        <!-- Content -->
        <div class="relative z-10">
          <!-- Header -->
          <div class="text-center mb-6 sm:mb-8 animate-slide-down">
            <div class="mx-auto h-14 w-14 sm:h-16 sm:w-16 rounded-2xl flex items-center justify-center shadow-lg animate-float mb-4 transition-all duration-300"
                 :style="{ backgroundColor: 'var(--accent)', color: 'white' }">
              <svg class="w-7 h-7 sm:w-8 sm:h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
            </div>
            <h1 class="text-2xl sm:text-3xl font-bold mb-2 transition-colors duration-300"
                :style="{ color: 'var(--text)' }">
              Admin Panel
            </h1>
            <p class="text-sm transition-colors duration-300"
               :style="{ color: 'var(--text-secondary)' }">
              Sign in to manage your Home Server
            </p>
          </div>

          <!-- Form -->
          <form @submit.prevent="onSubmit" class="space-y-5 sm:space-y-6 animate-slide-up" style="animation-delay: 0.2s">
            <div class="space-y-4">
              <div class="group">
                <label class="block text-sm font-medium mb-2 transition-colors duration-300"
                       :style="{ color: 'var(--text-secondary)' }">
                  Email Address
                </label>
                <div class="relative">
                  <input
                    v-model="email"
                    type="email"
                    required
                    autocomplete="email"
                    class="w-full rounded-xl px-4 py-3 sm:py-3.5 outline-none transition-all duration-300 backdrop-blur-sm text-sm sm:text-base focus:ring-2 focus:ring-opacity-50"
                    :style="{ 
                      backgroundColor: 'var(--bg-secondary)', 
                      borderColor: 'var(--border)',
                      color: 'var(--text)',
                      '--tw-ring-color': 'var(--accent)'
                    }"
                    :class="[
                      'border focus:border-opacity-0',
                      'placeholder:opacity-60'
                    ]"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div class="group">
                <label class="block text-sm font-medium mb-2 transition-colors duration-300"
                       :style="{ color: 'var(--text-secondary)' }">
                  Password
                </label>
                <div class="relative">
                  <input
                    v-model="password"
                    type="password"
                    required
                    autocomplete="current-password"
                    class="w-full rounded-xl px-4 py-3 sm:py-3.5 outline-none transition-all duration-300 backdrop-blur-sm text-sm sm:text-base focus:ring-2 focus:ring-opacity-50"
                    :style="{ 
                      backgroundColor: 'var(--bg-secondary)', 
                      borderColor: 'var(--border)',
                      color: 'var(--text)',
                      '--tw-ring-color': 'var(--accent)'
                    }"
                    :class="[
                      'border focus:border-opacity-0',
                      'placeholder:opacity-60'
                    ]"
                    placeholder="••••••••••••"
                  />
                </div>
              </div>
            </div>

            <button
              :disabled="loading"
              class="btn-primary w-full group relative overflow-hidden rounded-xl font-semibold py-3 sm:py-3.5 px-6 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-opacity-50 disabled:opacity-60 disabled:cursor-not-allowed disabled:transform-none text-sm sm:text-base"
              :style="{ '--tw-ring-color': 'var(--accent)' }"
              type="submit"
            >
              <div class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div class="relative flex items-center justify-center gap-2">
                <svg
                  v-if="loading"
                  class="animate-spin h-4 w-4 sm:h-5 sm:w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none" viewBox="0 0 24 24"
                >
                  <circle class="opacity-25" cx="12" cy="12" r="10"
                    stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                </svg>
                <span>{{ loading ? "Signing in..." : "Sign In" }}</span>
              </div>
            </button>

            <Transition
              enter-active-class="transition-all duration-300"
              enter-from-class="opacity-0 scale-95 translate-y-2"
              enter-to-class="opacity-100 scale-100 translate-y-0"
              leave-active-class="transition-all duration-200"
              leave-from-class="opacity-100 scale-100 translate-y-0"
              leave-to-class="opacity-0 scale-95 translate-y-2"
            >
              <div v-if="errorMsg" class="p-3 sm:p-4 rounded-xl backdrop-blur-sm border transition-all duration-300"
                   :style="{ 
                     backgroundColor: 'rgba(239, 68, 68, 0.1)', 
                     borderColor: 'rgba(239, 68, 68, 0.3)' 
                   }">
                <p class="text-red-400 text-sm text-center font-medium">
                  {{ errorMsg }}
                </p>
              </div>
            </Transition>
          </form>

          <!-- Footer -->
          <div class="mt-6 sm:mt-8 text-center animate-fade-in" style="animation-delay: 0.4s">
            <p class="text-xs sm:text-sm transition-colors duration-300"
               :style="{ color: 'var(--text-muted)' }">
              Protected area. Authorized access only.
            </p>
            <div class="mt-3 sm:mt-4 flex items-center justify-center space-x-2">
              <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span class="text-xs transition-colors duration-300"
                    :style="{ color: 'var(--text-muted)' }">
                Secure Connection
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

