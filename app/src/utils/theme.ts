// Theme utilities for consistent styling across the app

export const theme = {
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      500: '#6366f1',
      600: '#4f46e5',
      700: '#4338ca',
      900: '#312e81',
    },
    accent: {
      50: '#ecfeff',
      100: '#cffafe',
      500: '#06b6d4',
      600: '#0891b2',
      700: '#0e7490',
    },
    glass: {
      bg: 'rgba(255, 255, 255, 0.1)',
      border: 'rgba(255, 255, 255, 0.2)',
      hover: 'rgba(255, 255, 255, 0.15)',
    }
  },
  
  gradients: {
    primary: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%)',
    secondary: 'linear-gradient(135deg, #0f0f23 0%, #1e1b4b 50%, #0f0f23 100%)',
    glass: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
  },
  
  shadows: {
    glow: '0 0 20px rgba(99, 102, 241, 0.3)',
    glowLg: '0 0 40px rgba(99, 102, 241, 0.4)',
    glass: '0 10px 30px rgba(0,0,0,0.15)',
  },
  
  animations: {
    durations: {
      fast: '200ms',
      normal: '300ms',
      slow: '500ms',
    },
    easings: {
      smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    }
  }
}

// Utility functions for dynamic styling
export const getGlassStyle = (opacity = 0.1) => ({
  backgroundColor: `rgba(255, 255, 255, ${opacity})`,
  backdropFilter: 'blur(12px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
})

export const getGradientText = (gradient = theme.gradients.primary) => ({
  background: gradient,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
})

export const getGlowEffect = (color = '99, 102, 241', intensity = 0.3) => ({
  boxShadow: `0 0 20px rgba(${color}, ${intensity})`,
})
