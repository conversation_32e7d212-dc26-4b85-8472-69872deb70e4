import { createRouter, createWebHistory, type RouteRecordRaw } from "vue-router";
import { auth, initAuth } from "@/auth";

const routes: RouteRecordRaw[] = [
  {
    path: "/login",
    name: "login",
    component: () => import("@/views/LoginView.vue"),
    meta: { public: true },
  },
  {
    path: "/profile",
    name: "profile",
    component: () => import("@/views/ProfileView.vue"),
    meta: { requiresAuth: true },
  },
  // Default redirect to profile (protected)
  { path: "/", redirect: "/profile" },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

router.beforeEach(async (to) => {
  // Ensure session restoration (only fetched once)
  await initAuth();

  // Fetch session on navigation; better-auth caches reasonably
  const session = await auth.getSession();

  const isLoggedIn = Boolean(session?.user);
  const isPublic = Boolean(to.meta?.public);

  if (to.meta?.requiresAuth && !isLoggedIn) {
    return { name: "login", query: { redirect: to.fullPath } };
  }

  if (to.name === "login" && isLoggedIn) {
    return { name: "profile" };
  }

  return true;
});

export default router;

