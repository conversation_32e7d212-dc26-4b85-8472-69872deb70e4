<script setup lang="ts">
import { computed } from 'vue'
import { useTheme } from '@/composables/useTheme'

const { isDark } = useTheme()

// Responsive sizes based on screen size
const responsiveShapes = computed(() => ({
  large: 'w-32 h-32 sm:w-48 sm:h-48 lg:w-64 lg:h-64',
  medium: 'w-24 h-24 sm:w-32 sm:h-32 lg:w-48 lg:h-48',
  small: 'w-16 h-16 sm:w-24 sm:h-24 lg:w-32 lg:h-32'
}))
</script>

<template>
  <div class="animated-background-container">
    <!-- Debug info - remove after testing -->
    <div class="debug-info">
      <span>BG Active: {{ isDark ? 'Dark' : 'Light' }}</span>
    </div>

    <!-- Base animated gradient background -->
    <div class="base-gradient"
         :style="isDark
           ? {
               background: 'linear-gradient(135deg, #0f172a 0%, #581c87 40%, #312e81 100%)',
               backgroundSize: '400% 400%',
               animation: 'gradientShift 12s ease-in-out infinite'
             }
           : {
               background: 'linear-gradient(135deg, #eff6ff 0%, #e0e7ff 40%, #f3e8ff 100%)',
               backgroundSize: '400% 400%',
               animation: 'gradientShift 12s ease-in-out infinite'
             }
         ">
    </div>

    <!-- Overlay gradient for depth -->
    <div class="base-gradient transition-opacity duration-1000"
         :style="isDark
           ? { background: 'radial-gradient(circle, transparent 0%, rgba(147, 51, 234, 0.3) 50%, rgba(51, 65, 85, 0.6) 100%)' }
           : { background: 'radial-gradient(circle, transparent 0%, rgba(219, 234, 254, 0.3) 50%, rgba(224, 231, 255, 0.4) 100%)' }
         ">
    </div>

    <!-- Animated geometric shapes -->
    <div class="base-gradient">
      <!-- Large floating circles - responsive sizes -->
      <div class="absolute top-1/4 left-1/4 rounded-full blur-2xl sm:blur-3xl animate-float transition-all duration-1000"
           :class="[
             responsiveShapes.large,
             isDark 
               ? 'bg-gradient-to-br from-blue-500/20 to-purple-600/20' 
               : 'bg-gradient-to-br from-blue-200/40 to-purple-300/40'
           ]">
      </div>
      
      <div class="absolute bottom-1/4 right-1/4 rounded-full blur-2xl sm:blur-3xl animate-float transition-all duration-1000"
           :class="[
             'w-40 h-40 sm:w-60 sm:h-60 lg:w-80 lg:h-80',
             isDark 
               ? 'bg-gradient-to-br from-cyan-500/15 to-blue-600/15' 
               : 'bg-gradient-to-br from-cyan-200/30 to-blue-300/30'
           ]"
           style="animation-delay: 2s; animation-duration: 4s;">
      </div>
      
      <div class="absolute top-3/4 left-1/2 rounded-full blur-2xl sm:blur-3xl animate-float transition-all duration-1000"
           :class="[
             responsiveShapes.medium,
             isDark 
               ? 'bg-gradient-to-br from-purple-500/15 to-pink-600/15' 
               : 'bg-gradient-to-br from-purple-200/35 to-pink-300/35'
           ]"
           style="animation-delay: 1s; animation-duration: 5s;">
      </div>
      
      <!-- Additional shapes for larger screens -->
      <div class="hidden lg:block absolute top-1/6 right-1/6 w-36 h-36 rounded-full blur-3xl animate-float transition-all duration-1000"
           :class="isDark 
             ? 'bg-gradient-to-br from-indigo-500/10 to-violet-600/10' 
             : 'bg-gradient-to-br from-indigo-200/25 to-violet-300/25'
           "
           style="animation-delay: 3s; animation-duration: 6s;">
      </div>
      
      <!-- Grid pattern overlay -->
      <div class="absolute inset-0 w-full h-full transition-opacity duration-1000"
           :class="isDark ? 'opacity-5' : 'opacity-10'"
           :style="isDark
             ? {
                 backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)',
                 backgroundSize: '50px 50px'
               }
             : {
                 backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(99,102,241,0.2) 1px, transparent 0)',
                 backgroundSize: '60px 60px'
               }
           ">
      </div>
      
      <!-- Floating particles - responsive and theme-aware -->
      <div class="absolute top-1/6 left-1/6 w-1 h-1 sm:w-2 sm:h-2 rounded-full animate-float transition-all duration-1000"
           :class="isDark ? 'bg-blue-400 opacity-60' : 'bg-blue-500 opacity-40'">
      </div>
      <div class="absolute top-1/3 right-1/5 w-1 h-1 rounded-full animate-float transition-all duration-1000"
           :class="isDark ? 'bg-purple-400 opacity-40' : 'bg-purple-500 opacity-30'"
           style="animation-delay: 1s; animation-duration: 4s;">
      </div>
      <div class="absolute top-2/3 left-3/4 w-2 h-2 sm:w-3 sm:h-3 rounded-full animate-float transition-all duration-1000"
           :class="isDark ? 'bg-cyan-400 opacity-50' : 'bg-cyan-500 opacity-35'"
           style="animation-delay: 2s; animation-duration: 3s;">
      </div>
      <div class="absolute bottom-1/4 left-1/3 w-1 h-1 sm:w-2 sm:h-2 rounded-full animate-float transition-all duration-1000"
           :class="isDark ? 'bg-indigo-400 opacity-30' : 'bg-indigo-500 opacity-25'"
           style="animation-delay: 0.5s; animation-duration: 6s;">
      </div>
      <div class="absolute top-1/2 right-1/3 w-1 h-1 rounded-full animate-float transition-all duration-1000"
           :class="isDark ? 'bg-pink-400 opacity-50' : 'bg-pink-500 opacity-30'"
           style="animation-delay: 3s; animation-duration: 4s;">
      </div>
      <div class="absolute bottom-1/3 right-1/6 w-1 h-1 sm:w-2 sm:h-2 rounded-full animate-float transition-all duration-1000"
           :class="isDark ? 'bg-violet-400 opacity-40' : 'bg-violet-500 opacity-25'"
           style="animation-delay: 1.5s; animation-duration: 5s;">
      </div>
      
      <!-- Subtle light rays - theme aware -->
      <div class="absolute top-0 left-1/4 w-px h-full transform rotate-12 animate-pulse transition-all duration-1000"
           :class="isDark 
             ? 'bg-gradient-to-b from-transparent via-blue-400/20 to-transparent' 
             : 'bg-gradient-to-b from-transparent via-blue-300/15 to-transparent'
           ">
      </div>
      <div class="absolute top-0 right-1/3 w-px h-full transform -rotate-12 animate-pulse transition-all duration-1000"
           :class="isDark 
             ? 'bg-gradient-to-b from-transparent via-purple-400/20 to-transparent' 
             : 'bg-gradient-to-b from-transparent via-purple-300/15 to-transparent'
           "
           style="animation-delay: 2s;">
      </div>
      
      <!-- Additional rays for larger screens -->
      <div class="hidden md:block absolute top-0 left-1/2 w-px h-full transform rotate-6 animate-pulse transition-all duration-1000"
           :class="isDark 
             ? 'bg-gradient-to-b from-transparent via-cyan-400/15 to-transparent' 
             : 'bg-gradient-to-b from-transparent via-cyan-300/10 to-transparent'
           "
           style="animation-delay: 4s;">
      </div>
    </div>
    
    <!-- Noise texture overlay for depth -->
    <div class="absolute inset-0 mix-blend-overlay transition-opacity duration-1000"
         :class="isDark ? 'opacity-[0.02]' : 'opacity-[0.015]'"
         style="background-image: url('data:image/svg+xml,%3Csvg viewBox=\'0 0 256 256\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cfilter id=\'noiseFilter\'%3E%3CfeTurbulence type=\'fractalNoise\' baseFrequency=\'0.9\' numOctaves=\'4\' stitchTiles=\'stitch\'/%3E%3C/filter%3E%3Crect width=\'100%25\' height=\'100%25\' filter=\'url(%23noiseFilter)\'/%3E%3C/svg%3E');">
    </div>
  </div>
</template>

<style scoped>
/* Ensure the background container has proper dimensions */
.animated-background-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  z-index: -10;
  overflow: hidden;
}

.base-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}

.debug-info {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-shift {
  animation: gradientShift 12s ease-in-out infinite;
}

/* Enhanced float animation with more natural movement */
@keyframes float {
  0%, 100% { 
    transform: translateY(0px) translateX(0px) rotate(0deg); 
  }
  25% { 
    transform: translateY(-10px) translateX(5px) rotate(1deg); 
  }
  50% { 
    transform: translateY(-20px) translateX(0px) rotate(0deg); 
  }
  75% { 
    transform: translateY(-10px) translateX(-5px) rotate(-1deg); 
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}
</style>
