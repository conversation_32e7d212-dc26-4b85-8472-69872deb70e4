<script setup lang="ts">
import { ref, onMounted } from 'vue'

const props = defineProps<{
  show: boolean
}>()

const progress = ref(0)
const loadingText = ref('Initializing...')

const loadingSteps = [
  'Initializing...',
  'Loading components...',
  'Connecting to server...',
  'Authenticating...',
  'Ready!'
]

onMounted(() => {
  if (props.show) {
    startLoading()
  }
})

function startLoading() {
  let step = 0
  const interval = setInterval(() => {
    progress.value += Math.random() * 20 + 10
    
    if (step < loadingSteps.length - 1) {
      loadingText.value = loadingSteps[step]
      step++
    }
    
    if (progress.value >= 100) {
      progress.value = 100
      loadingText.value = loadingSteps[loadingSteps.length - 1]
      clearInterval(interval)
    }
  }, 300)
}
</script>

<template>
  <Transition
    enter-active-class="transition-opacity duration-300"
    leave-active-class="transition-opacity duration-500"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="show"
      class="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"
    >
      <!-- Animated background particles -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400 rounded-full animate-float opacity-60"></div>
        <div class="absolute top-3/4 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-float opacity-40" style="animation-delay: 1s"></div>
        <div class="absolute top-1/2 left-3/4 w-3 h-3 bg-cyan-400 rounded-full animate-float opacity-50" style="animation-delay: 2s"></div>
        <div class="absolute bottom-1/4 left-1/2 w-2 h-2 bg-indigo-400 rounded-full animate-float opacity-30" style="animation-delay: 0.5s"></div>
      </div>

      <!-- Loading content -->
      <div class="relative z-10 text-center">
        <!-- Logo/Icon -->
        <div class="mb-8 animate-scale-in">
          <div class="mx-auto w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-glow animate-pulse-glow">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
          </div>
        </div>

        <!-- Title -->
        <h1 class="text-4xl font-bold text-white mb-2 animate-slide-down">
          Home Server
        </h1>
        <p class="text-xl text-gray-300 mb-12 animate-slide-down" style="animation-delay: 0.2s">
          Admin Panel
        </p>

        <!-- Progress bar -->
        <div class="w-80 mx-auto mb-6 animate-slide-up" style="animation-delay: 0.4s">
          <div class="bg-gray-700 rounded-full h-2 overflow-hidden">
            <div 
              class="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-300 ease-out"
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
          <div class="flex justify-between text-sm text-gray-400 mt-2">
            <span>{{ Math.round(progress) }}%</span>
            <span>Loading...</span>
          </div>
        </div>

        <!-- Loading text -->
        <p class="text-gray-300 animate-fade-in" style="animation-delay: 0.6s">
          {{ loadingText }}
        </p>

        <!-- Spinner -->
        <div class="mt-8 flex justify-center animate-fade-in" style="animation-delay: 0.8s">
          <div class="w-8 h-8 border-2 border-gray-600 border-t-blue-500 rounded-full animate-spin"></div>
        </div>
      </div>
    </div>
  </Transition>
</template>
