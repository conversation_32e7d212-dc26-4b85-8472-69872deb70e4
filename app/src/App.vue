<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Icon } from '@iconify/vue'
import LoadingScreen from '@/components/LoadingScreen.vue'
import AnimatedBackground from '@/components/AnimatedBackground.vue'
import { useTheme } from '@/composables/useTheme'

const isLoading = ref(true)
const showContent = ref(false)
const { isDark, toggleTheme, initTheme } = useTheme()

onMounted(() => {
  initTheme()
  // Simulate initial loading
  setTimeout(() => {
    isLoading.value = false
    // Small delay to show the fade transition
    setTimeout(() => {
      showContent.value = true
    }, 100)
  }, 1200) // shorter loading time
})
</script>

<template>
  <div id="app" class="min-h-screen relative transition-all duration-300 ease-in-out"
       :style="{ 
         backgroundColor: 'var(--bg)', 
         color: 'var(--text)' 
       }">
    
    <!-- Animated Background - now visible in both modes -->
    <AnimatedBackground />

    <!-- Theme toggle -->
    <div class="fixed top-4 right-4 z-50">
      <button
        @click="toggleTheme"
        class="group flex items-center gap-2 rounded-full backdrop-blur-md px-3 py-2 sm:px-4 sm:py-2.5 
               transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl
               glass border border-white/20 hover:border-white/30
               hover:scale-105 active:scale-95"
        :style="{ 
          backgroundColor: 'var(--glass-bg)', 
          color: 'var(--text)',
          borderColor: 'var(--glass-border)'
        }"
        :aria-label="isDark ? 'Switch to light mode' : 'Switch to dark mode'"
      >
        <Icon 
          :icon="isDark ? 'mdi:weather-sunny' : 'mdi:weather-night'" 
          class="w-5 h-5 transition-transform duration-300"
          :class="{ 'rotate-180': isDark }"
        />
        <span class="text-sm font-medium hidden sm:inline transition-all duration-300">
          {{ isDark ? 'Light' : 'Dark' }}
        </span>
      </button>
    </div>

    <!-- Loading Screen -->
    <LoadingScreen :show="isLoading" />

    <!-- Main Content -->
    <Transition
      enter-active-class="transition-all duration-700 ease-out"
      enter-from-class="opacity-0 scale-95 blur-sm"
      enter-to-class="opacity-100 scale-100 blur-0"
      leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="opacity-100 scale-100 blur-0"
      leave-to-class="opacity-0 scale-105 blur-sm"
    >
      <div v-if="showContent" class="relative z-10">
        <router-view />
      </div>
    </Transition>
  </div>
</template>

<style>
#app {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/***** Scrollbar *****/
::-webkit-scrollbar { width: 8px; }
::-webkit-scrollbar-track { background: rgba(0, 0, 0, 0.05); }
::-webkit-scrollbar-thumb { background: rgba(0, 123, 255, 0.5); border-radius: 4px; }
::-webkit-scrollbar-thumb:hover { background: rgba(0, 123, 255, 0.7); }
</style>
